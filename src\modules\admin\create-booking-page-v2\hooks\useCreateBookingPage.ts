import { useRouter } from 'next/navigation'
import { useCallback, useState } from 'react'
import { toast } from 'sonner'
import { useCreateBookingPageV2Store } from '../stores/create-booking-page-v2.store'

interface CreateBookingPagePayload {
  template: {
    id: string
  }
  config: {
    bannerTitle: string
    bannerSubtitle: string
    bannerImage: string
    openTime: string
    closeTime: string
    fields: Array<{
      id: string
      name: string
      type: string
      capacity: number
    }>
  }
}

export const useCreateBookingPageV2 = () => {
  const router = useRouter()
  const [isCreating, setIsCreating] = useState(false)

  const {
    selectedTemplateId,
    bookingConfig,
    validateCurrentStep,
    reset,
  } = useCreateBookingPageV2Store()

  const createBookingPage = useCallback(async () => {
    // Validate all steps before creating
    if (!validateCurrentStep()) {
      toast.error('Vui lòng kiểm tra lại thông tin đã nhập')
      return false
    }

    setIsCreating(true)

    try {
      const payload: CreateBookingPagePayload = {
        template: {
          id: selectedTemplateId,
        },
        config: {
          bannerTitle: bookingConfig.bannerTitle,
          bannerSubtitle: bookingConfig.bannerSubtitle,
          bannerImage: bookingConfig.bannerImage,
          openTime: bookingConfig.openTime,
          closeTime: bookingConfig.closeTime,
          fields: bookingConfig.fields.map(field => ({
            id: field.id,
            name: field.name,
            type: field.type,
            capacity: field.capacity,
          })),
        },
      }

      // TODO: Replace with actual API call
      // eslint-disable-next-line no-console
      console.log('Creating booking page with payload:', payload)

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Success
      toast.success('Trang đặt chỗ đã được tạo thành công!')

      // Reset form
      reset()

      // Redirect to booking page list or edit page
      router.push('/admin/booking-pages')

      return true
    } catch (error) {
      console.error('Error creating booking page:', error)
      toast.error('Có lỗi xảy ra khi tạo trang đặt chỗ. Vui lòng thử lại.')
      return false
    } finally {
      setIsCreating(false)
    }
  }, [
    selectedTemplateId,
    bookingConfig,
    validateCurrentStep,
    reset,
    router,
  ])

  return {
    createBookingPage,
    isCreating,
  }
}
