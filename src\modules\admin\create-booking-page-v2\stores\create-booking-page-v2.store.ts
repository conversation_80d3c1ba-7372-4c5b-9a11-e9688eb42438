import type { BookingConfig, BookingField, CreateBookingPageV2State, PageInfo } from '../types'
import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'

// ===== CONSTANTS =====
const initialPageInfo: PageInfo = {
  name: '',
  description: '',
  slug: '',
}

const initialBookingConfig: BookingConfig = {
  // Banner settings
  bannerTitle: 'Đặt sân thể thao',
  bannerSubtitle: 'Đặt sân nhanh chóng và dễ dàng',
  bannerImage: '',

  // Operating hours
  openTime: '06:00',
  closeTime: '22:00',

  // Fields configuration
  fields: [
    {
      id: 'field-1',
      name: 'Sân 1',
      type: 'football',
      capacity: 1,
    },
  ],

  // Description settings
  description: 'Hệ thống đặt sân thể thao chuyên nghiệp, đảm bảo trải nghiệm tốt nhất cho khách hàng.',
  location: 'Hà Nội, Việt Nam',

  // Contact information
  contactInfo: {
    phone: '',
    email: '',
    address: '',
    socialLinks: {
      facebook: '',
      instagram: '',
      website: '',
    },
  },

  // Pricing configuration
  pricing: {
    basePrice: 300000,
    currency: 'VNĐ',
    priceUnit: 'hour',
    showPricing: true,
  },

  // Display settings
  showCapacity: true,
  showFieldTypes: true,
  showDirections: true,
}

// ===== TEMPLATE HELPERS =====
const getTemplateConfigs = (): Record<string, Partial<BookingConfig>> => ({
  'sport-football': {
    bannerTitle: 'Đặt sân bóng đá',
    bannerSubtitle: 'Sân bóng đá chất lượng cao',
    description: 'Hệ thống đặt sân bóng đá chuyên nghiệp với các sân cỏ nhân tạo chất lượng cao.',
    fields: [
      { id: 'field-1', name: 'Sân bóng 1', type: 'football', capacity: 1 },
      { id: 'field-2', name: 'Sân bóng 2', type: 'football', capacity: 1 },
    ],
    pricing: {
      basePrice: 300000,
      currency: 'VNĐ',
      priceUnit: 'hour',
      showPricing: true,
    },
  },
  'sport-tennis': {
    bannerTitle: 'Đặt sân tennis',
    bannerSubtitle: 'Sân tennis đẳng cấp quốc tế',
    description: 'Hệ thống đặt sân tennis với các sân đẳng cấp quốc tế, phù hợp cho mọi trình độ.',
    fields: [
      { id: 'field-1', name: 'Sân tennis 1', type: 'tennis', capacity: 1 },
      { id: 'field-2', name: 'Sân tennis 2', type: 'tennis', capacity: 1 },
    ],
    pricing: {
      basePrice: 500000,
      currency: 'VNĐ',
      priceUnit: 'hour',
      showPricing: true,
    },
  },
  'sport-badminton': {
    bannerTitle: 'Đặt sân cầu lông',
    bannerSubtitle: 'Sân cầu lông tiêu chuẩn',
    description: 'Hệ thống đặt sân cầu lông với các sân tiêu chuẩn, ánh sáng tốt.',
    fields: [
      { id: 'field-1', name: 'Sân cầu lông 1', type: 'badminton', capacity: 1 },
      { id: 'field-2', name: 'Sân cầu lông 2', type: 'badminton', capacity: 1 },
      { id: 'field-3', name: 'Sân cầu lông 3', type: 'badminton', capacity: 1 },
    ],
    pricing: {
      basePrice: 200000,
      currency: 'VNĐ',
      priceUnit: 'hour',
      showPricing: true,
    },
  },
  'sport-basketball': {
    bannerTitle: 'Đặt sân bóng rổ',
    bannerSubtitle: 'Sân bóng rổ trong nhà',
    description: 'Hệ thống đặt sân bóng rổ trong nhà với sàn gỗ chuyên nghiệp.',
    fields: [
      { id: 'field-1', name: 'Sân bóng rổ 1', type: 'basketball', capacity: 1 },
    ],
    pricing: {
      basePrice: 400000,
      currency: 'VNĐ',
      priceUnit: 'hour',
      showPricing: true,
    },
  },
  'event-conference': {
    bannerTitle: 'Đặt phòng họp',
    bannerSubtitle: 'Phòng họp chuyên nghiệp',
    description: 'Hệ thống đặt phòng họp với trang thiết bị hiện đại, phù hợp cho các cuộc họp quan trọng.',
    fields: [
      { id: 'field-1', name: 'Phòng họp A', type: 'football', capacity: 20 },
      { id: 'field-2', name: 'Phòng họp B', type: 'football', capacity: 10 },
    ],
    pricing: {
      basePrice: 1000000,
      currency: 'VNĐ',
      priceUnit: 'hour',
      showPricing: true,
    },
    showCapacity: true,
    showFieldTypes: false,
    showDirections: true,
  },
  'restaurant-booking': {
    bannerTitle: 'Đặt bàn nhà hàng',
    bannerSubtitle: 'Nhà hàng sang trọng',
    description: 'Hệ thống đặt bàn nhà hàng với không gian sang trọng, ẩm thực đa dạng.',
    fields: [
      { id: 'field-1', name: 'Bàn 2 người', type: 'football', capacity: 2 },
      { id: 'field-2', name: 'Bàn 4 người', type: 'football', capacity: 4 },
      { id: 'field-3', name: 'Bàn 6 người', type: 'football', capacity: 6 },
    ],
    pricing: {
      basePrice: 0,
      currency: 'VNĐ',
      priceUnit: 'session',
      showPricing: false,
    },
    showCapacity: true,
    showFieldTypes: false,
    showDirections: true,
  },
})

const getTemplateConfig = (templateId: string): Partial<BookingConfig> | null => {
  const configs = getTemplateConfigs()
  return configs[templateId] || null
}

// ===== FIELD HELPERS =====
const createNewField = (existingFields: BookingField[]): BookingField => ({
  id: `field-${Date.now()}`,
  name: `Sân ${existingFields.length + 1}`,
  type: 'football',
  capacity: 1,
})

const updateFieldInArray = (
  fields: BookingField[],
  fieldId: string,
  updates: Partial<BookingField>,
): BookingField[] => {
  return fields.map(field =>
    field.id === fieldId ? { ...field, ...updates } : field,
  )
}

const removeFieldFromArray = (fields: BookingField[], fieldId: string): BookingField[] => {
  return fields.filter(field => field.id !== fieldId)
}

// ===== VALIDATION HELPERS =====
const getValidationDataForStep = (
  step: number,
  pageInfo: PageInfo,
  selectedTemplateId: string,
  bookingConfig: BookingConfig,
): any => {
  switch (step) {
    case 1:
      return pageInfo
    case 2:
      return { selectedTemplateId }
    case 3:
      return bookingConfig
    default:
      return null
  }
}

const validateCurrentStepData = (
  step: number,
  pageInfo: PageInfo,
  selectedTemplateId: string,
  bookingConfig: BookingConfig,
): boolean => {
  const data = getValidationDataForStep(step, pageInfo, selectedTemplateId, bookingConfig)
  if (!data) {
    return false
  }

  return true
}

// ===== STATE HELPERS =====
const clearErrorsForKeys = (errors: Record<string, string>, keys: string[]): Record<string, string> => {
  return keys.reduce((acc, key) => ({ ...acc, [key]: '' }), { ...errors })
}

const hasConfigChanges = (currentConfig: BookingConfig, newConfig: Partial<BookingConfig>): boolean => {
  return Object.keys(newConfig).some(key =>
    currentConfig[key as keyof BookingConfig] !== newConfig[key as keyof BookingConfig],
  )
}

// ===== MIGRATION HELPERS =====
const migrateBookingConfig = (persistedConfig: any, version: number): any => {
  if (version === 0 || version === 1) {
    return {
      ...persistedConfig,
      description: persistedConfig?.description || '',
      location: persistedConfig?.location || '',
      contactInfo: persistedConfig?.contactInfo || {
        phone: '',
        email: '',
        address: '',
        socialLinks: {
          facebook: '',
          instagram: '',
          website: '',
        },
      },
      pricing: persistedConfig?.pricing || {
        basePrice: 300000,
        currency: 'VNĐ',
        priceUnit: 'hour',
        showPricing: true,
      },
      showCapacity: persistedConfig?.showCapacity ?? true,
      showFieldTypes: persistedConfig?.showFieldTypes ?? true,
      showDirections: persistedConfig?.showDirections ?? true,
    }
  }
  return persistedConfig
}

const migrateState = (persistedState: any, version: number): any => {
  if (version === 0 || version === 1) {
    return {
      ...persistedState,
      bookingConfig: migrateBookingConfig(persistedState.bookingConfig, version),
    }
  }
  return persistedState
}

// ===== ZUSTAND STORE =====
export const useCreateBookingPageV2Store = create<CreateBookingPageV2State>()(
  devtools(
    persist(
      (set, get) => ({
        // State
        currentStep: 1,
        pageInfo: initialPageInfo,
        selectedTemplateId: '',
        bookingConfig: initialBookingConfig,
        isLoading: false,
        errors: {},

        // Step navigation actions
        setCurrentStep: (step: number) => {
          set({ currentStep: step }, false, 'setCurrentStep')
        },

        nextStep: () => {
          const { currentStep } = get()
          if (currentStep < 3) {
            set({ currentStep: currentStep + 1, errors: {} }, false, 'nextStep')
          }
        },

        prevStep: () => {
          const { currentStep } = get()
          if (currentStep > 1) {
            set({ currentStep: currentStep - 1, errors: {} }, false, 'prevStep')
          }
        },

        // Page info actions
        updatePageInfo: (info: Partial<PageInfo>) => {
          set(
            state => ({
              pageInfo: { ...state.pageInfo, ...info },
              errors: clearErrorsForKeys(state.errors, Object.keys(info)),
            }),
            false,
            'updatePageInfo',
          )
        },

        // Template actions
        setSelectedTemplate: (templateId: string) => {
          set(
            state => ({
              selectedTemplateId: templateId,
              errors: clearErrorsForKeys(state.errors, ['selectedTemplateId']),
            }),
            false,
            'setSelectedTemplate',
          )
          // Auto load template config
          get().loadTemplateConfig(templateId)
        },

        loadTemplateConfig: (templateId: string) => {
          const config = getTemplateConfig(templateId)
          if (config) {
            set(
              state => ({
                bookingConfig: { ...state.bookingConfig, ...config },
                errors: clearErrorsForKeys(state.errors, Object.keys(config)),
              }),
              false,
              'loadTemplateConfig',
            )
          }
        },

        getTemplateConfig: (templateId: string) => {
          return getTemplateConfig(templateId)
        },

        // Booking config actions
        updateBookingConfig: (config: Partial<BookingConfig>) => {
          set(
            (state) => {
              if (!hasConfigChanges(state.bookingConfig, config)) {
                return state
              }

              return {
                bookingConfig: { ...state.bookingConfig, ...config },
                errors: clearErrorsForKeys(state.errors, Object.keys(config)),
              }
            },
            false,
            'updateBookingConfig',
          )
        },

        // Field management actions
        addField: () => {
          const { bookingConfig } = get()
          const newField = createNewField(bookingConfig.fields)

          set(
            state => ({
              bookingConfig: {
                ...state.bookingConfig,
                fields: [...state.bookingConfig.fields, newField],
              },
              errors: clearErrorsForKeys(state.errors, ['fields']),
            }),
            false,
            'addField',
          )
        },

        removeField: (fieldId: string) => {
          set(
            state => ({
              bookingConfig: {
                ...state.bookingConfig,
                fields: removeFieldFromArray(state.bookingConfig.fields, fieldId),
              },
            }),
            false,
            'removeField',
          )
        },

        updateField: (fieldId: string, updates: Partial<BookingField>) => {
          set(
            state => ({
              bookingConfig: {
                ...state.bookingConfig,
                fields: updateFieldInArray(state.bookingConfig.fields, fieldId, updates),
              },
            }),
            false,
            'updateField',
          )
        },

        // Validation actions
        validateCurrentStep: () => {
          const { currentStep, pageInfo, selectedTemplateId, bookingConfig } = get()
          return validateCurrentStepData(currentStep, pageInfo, selectedTemplateId, bookingConfig)
        },

        // Reset actions
        reset: () => {
          set(
            {
              currentStep: 1,
              pageInfo: initialPageInfo,
              selectedTemplateId: '',
              bookingConfig: initialBookingConfig,
              isLoading: false,
              errors: {},
            },
            false,
            'reset',
          )
        },

        resetToDefaultConfig: () => {
          set(
            { bookingConfig: initialBookingConfig, errors: {} },
            false,
            'resetToDefaultConfig',
          )
        },

        clearStorage: () => {
          localStorage.removeItem('create-booking-page-v2-store')
          get().reset()
        },
      }),
      {
        name: 'create-booking-page-v2-store',
        partialize: state => ({
          currentStep: state.currentStep,
          pageInfo: state.pageInfo,
          selectedTemplateId: state.selectedTemplateId,
          bookingConfig: state.bookingConfig,
        }),
        version: 2,
        migrate: migrateState,
      },
    ),
    {
      name: 'create-booking-page-v2-store',
    },
  ),
)
