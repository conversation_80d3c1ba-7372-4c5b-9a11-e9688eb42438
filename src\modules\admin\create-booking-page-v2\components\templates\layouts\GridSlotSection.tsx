'use client'

import type { GridSlotSectionProps } from './types'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { cn } from '@/lib/utils'
import { CalendarIcon, CheckCircle, Clock } from 'lucide-react'
import React, { useState } from 'react'

const DEFAULT_SELECTED_DATE = new Date()

const GridSlotSection: React.FC<GridSlotSectionProps> = ({
  config,
  previewMode = 'desktop',
  className,
  selectedDate = DEFAULT_SELECTED_DATE,
  onDateChange,
  onSlotSelect,
  selectedSlots = {},
}) => {
  const [localSelectedSlots, setLocalSelectedSlots] = useState<Record<string, string[]>>({})
  const isMobile = previewMode === 'mobile'

  // Generate time slots based on config open/close time
  const generateTimeSlots = () => {
    const slots = []
    const openHour = Number.parseInt((config.openTime as any || '00:00').split(':')[0])
    const closeHour = Number.parseInt((config.closeTime as any || '00:00').split(':')[0])

    for (let hour = openHour; hour < closeHour; hour++) {
      slots.push(`${hour.toString().padStart(2, '0')}:00`)
      slots.push(`${hour.toString().padStart(2, '0')}:30`)
    }
    return slots
  }

  const timeSlots = generateTimeSlots()

  const handleSlotClick = (fieldId: string, timeSlot: string) => {
    const currentSlots = localSelectedSlots[fieldId] || []
    const isSelected = currentSlots.includes(timeSlot)

    let newSlots
    if (isSelected) {
      newSlots = currentSlots.filter(slot => slot !== timeSlot)
    } else {
      newSlots = [...currentSlots, timeSlot]
    }

    const updatedSelectedSlots = {
      ...localSelectedSlots,
      [fieldId]: newSlots,
    }

    setLocalSelectedSlots(updatedSelectedSlots)
    onSlotSelect?.(fieldId, timeSlot)
  }

  const isSlotSelected = (fieldId: string, timeSlot: string) => {
    return (localSelectedSlots[fieldId] || []).includes(timeSlot)
      || (selectedSlots[fieldId] || []).includes(timeSlot)
  }

  const isSlotBooked = (_: string, timeSlot: string) => {
    // Mock some booked slots for demo
    const bookedSlots = ['08:00', '09:00', '14:00', '15:30', '19:00']
    return bookedSlots.includes(timeSlot)
  }

  const getSlotStatus = (fieldId: string, timeSlot: string) => {
    if (isSlotBooked(fieldId, timeSlot)) {
      return 'booked'
    }
    if (isSlotSelected(fieldId, timeSlot)) {
      return 'selected'
    }
    return 'available'
  }

  const getSlotButtonClass = (status: string) => {
    switch (status) {
      case 'booked':
        return 'bg-gray-200 text-gray-500 cursor-not-allowed border-gray-300'
      case 'selected':
        return 'bg-orange-500 text-white border-orange-500 hover:bg-orange-600'
      case 'available':
      default:
        return 'bg-white text-gray-700 border-gray-300 hover:bg-orange-50 hover:border-orange-300'
    }
  }

  const renderGrid = () => {
    if (config.fields && config.fields.length > 0) {
      return (
        <div className="space-y-4">
          {config.fields.map(field => (
            <Card key={field.id} className="border-orange-200">
              <CardHeader className="pb-3">
                <CardTitle className={cn(
                  'flex items-center justify-between',
                  isMobile ? 'text-base' : 'text-lg',
                )}
                >
                  <div className="flex items-center gap-2">
                    <span className="text-xl">
                      {field.type === 'football' && '⚽'}
                      {field.type === 'tennis' && '🎾'}
                      {field.type === 'badminton' && '🏸'}
                      {field.type === 'basketball' && '🏀'}
                    </span>
                    {field.name}
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className={cn(
                  'grid gap-2',
                  isMobile ? 'grid-cols-4' : 'grid-cols-6 lg:grid-cols-8',
                )}
                >
                  {timeSlots.map((timeSlot) => {
                    const status = getSlotStatus(field.id, timeSlot)
                    const isDisabled = status === 'booked'

                    return (
                      <Button
                        key={timeSlot}
                        variant="outline"
                        size="sm"
                        disabled={isDisabled}
                        className={cn(
                          'relative transition-all duration-200',
                          getSlotButtonClass(status),
                          isMobile ? 'h-8 text-xs px-2' : 'h-10 text-sm px-3',
                        )}
                        onClick={() => !isDisabled && handleSlotClick(field.id, timeSlot)}
                      >
                        {timeSlot}
                        {status === 'selected' && (
                          <CheckCircle className="absolute -top-1 -right-1 w-3 h-3 text-orange-500 bg-white rounded-full" />
                        )}
                      </Button>
                    )
                  })}
                </div>

                {/* Selected slots summary */}
                {(localSelectedSlots[field.id]?.length || 0) > 0 && (
                  <div className="mt-3 p-2 bg-orange-50 rounded-lg">
                    <p className={cn(
                      'text-orange-700 font-medium',
                      isMobile ? 'text-xs' : 'text-sm',
                    )}
                    >
                      Đã chọn:
                      {' '}
                      {localSelectedSlots[field.id]?.join(', ')}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )
    }

    return (
      <Card className="border-orange-200">
        <CardContent className="p-8 text-center">
          <div className="text-gray-400 mb-2">
            <Clock className="w-12 h-12 mx-auto" />
          </div>
          <p className="text-gray-600">
            Chưa có sân nào được cấu hình. Vui lòng thêm sân để hiển thị lịch đặt.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn('space-y-6', className)}>
      <div className={cn(
        'flex items-center justify-between',
        isMobile ? 'flex-col gap-4' : 'flex-row',
      )}
      >
        <h2 className={cn(
          'font-bold text-gray-900',
          isMobile ? 'text-lg' : 'text-xl',
        )}
        >
          Lịch đặt sân
        </h2>

        {/* Date Picker */}
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                'justify-start text-left font-normal border-orange-300 hover:bg-orange-50',
                !selectedDate && 'text-muted-foreground',
                isMobile ? 'w-full' : 'w-auto',
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4 text-orange-500" />
              {selectedDate
                ? (
                    selectedDate.toLocaleDateString('vi-VN', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                    })
                  )
                : (
                    <span>Chọn ngày</span>
                  )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="end">
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={(date) => {
                if (date && onDateChange) {
                  onDateChange(date)
                }
              }}
              disabled={date =>
                date < new Date(new Date().setHours(0, 0, 0, 0))}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>

      {/* Legend */}
      <div className={cn(
        'flex gap-4 p-3 bg-gray-50 rounded-lg',
      )}
      >
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-white border border-gray-300 rounded"></div>
          <span className={cn('text-gray-600', isMobile ? 'text-xs' : 'text-sm')}>
            Có sẵn
          </span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-orange-500 rounded"></div>
          <span className={cn('text-gray-600', isMobile ? 'text-xs' : 'text-sm')}>
            Đã chọn
          </span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-gray-200 rounded"></div>
          <span className={cn('text-gray-600', isMobile ? 'text-xs' : 'text-sm')}>
            Đã đặt
          </span>
        </div>
      </div>

      {/* Time Slots Grid */}
      {renderGrid()}
    </div>
  )
}

export default GridSlotSection
