'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ooter, SidebarHeader } from '@/components/ui'
import { Button } from '@/components/ui/button'
import { clearAuthTokens } from '@/services/auth'
import { useRouter } from 'next/navigation'
import React from 'react'
import { NavProjects } from './NavMain'
import { TeamSwitcher } from './TeamSwitcher'

type Props = React.ComponentProps<typeof Sidebar>

const AppSidebar = ({ ...props }: Props) => {
  const router = useRouter()
  const handleLogout = () => {
    clearAuthTokens()
    // Nếu có API logout, có thể gọi ở đây
    router.push('/auth/signin')
  }
  return (
    <Sidebar
      collapsible="icon"
      {...props}
    >
      <SidebarHeader>
        <TeamSwitcher />
      </SidebarHeader>
      <SidebarContent>
        <NavProjects />
      </SidebarContent>
      <SidebarFooter>
        <Button variant="outline" className="w-full" onClick={handleLogout}>
          <PERSON><PERSON><PERSON> xuất
        </Button>
      </SidebarFooter>
    </Sidebar>
  )
}

export default AppSidebar
