'use client'

import type { BannerSectionProps } from './types'
import { cn } from '@/lib/utils'
import React from 'react'

const BannerSection: React.FC<BannerSectionProps> = ({
  config,
  previewMode = 'desktop',
  className,
}) => {
  const isMobile = previewMode === 'mobile'
  return (
    <div className={cn(
      'relative w-full overflow-hidden rounded-lg',
      isMobile ? 'h-48' : 'h-64 lg:h-80',
      className,
    )}
    >
      <div
        className="absolute inset-0 bg-contain bg-center bg-no-repeat"
        style={{
          backgroundImage: `url(${config.bannerImage || '/api/placeholder/800/400'})`,
          backgroundColor: config.bannerBackgroundColor,
          backgroundSize: config.bannerImageFit,

        }}
      >
      </div>

      {/* Content */}
      <div className="relative z-10 flex h-full items-center justify-center text-center text-white">
        <div className={cn(
          'max-w-4xl px-4',
          isMobile ? 'space-y-2' : 'space-y-4',
        )}
        >
          <h1 className={cn(
            'font-bold leading-tight',
            isMobile ? 'text-xl' : 'text-3xl lg:text-5xl',
          )}
          >
            {config.bannerTitle}
          </h1>
          <p className={cn(
            'text-gray-100',
            isMobile ? 'text-sm' : 'text-lg lg:text-xl',
          )}
          >
            {config.bannerSubtitle}
          </p>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r" />
    </div>
  )
}

export default BannerSection
